"""
IQ数据参数详细分析脚本
深入解释各个参数的物理意义和影响
"""

import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_system_parameters():
    """分析系统参数的物理意义和影响"""
    
    # 系统参数
    c = 3e8  # 光速 (m/s)
    F_C = 1e9  # 载波频率 (1 GHz)
    F_S = 1e6  # 采样频率 (1 MHz)
    T = 0.01  # 仿真时长 (s)
    
    print("=" * 100)
    print("IQ数据系统参数详细分析")
    print("=" * 100)
    
    print("\n📡 1. 载波频率 (F_C = 1 GHz)")
    print("-" * 50)
    wavelength = c / F_C
    print(f"   物理意义: 雷达发射的电磁波频率")
    print(f"   波长 λ = c/f = {wavelength:.3f} 米")
    print(f"   影响因素:")
    print(f"   • 穿透能力: 1GHz属于L波段，具有较好的穿透能力")
    print(f"   • 分辨率: 波长越短，理论分辨率越高")
    print(f"   • 多普勒敏感性: 频率越高，多普勒效应越明显")
    print(f"   • 传播损耗: 频率越高，自由空间传播损耗越大")
    
    print(f"\n📊 2. 采样频率 (F_S = 1 MHz)")
    print("-" * 50)
    sampling_interval = 1 / F_S
    nyquist_freq = F_S / 2
    print(f"   物理意义: IQ信号的数字化采样率")
    print(f"   采样间隔 Δt = 1/F_S = {sampling_interval*1e6:.1f} 微秒")
    print(f"   奈奎斯特频率 = F_S/2 = {nyquist_freq/1e3:.0f} kHz")
    print(f"   影响因素:")
    print(f"   • 信号带宽: 能够无失真采样的最大信号带宽为 {nyquist_freq/1e3:.0f} kHz")
    print(f"   • 时间分辨率: 采样间隔决定了时间测量精度")
    print(f"   • 数据量: 采样率越高，数据量越大")
    print(f"   • 多普勒测量范围: 最大可测多普勒频移为 ±{nyquist_freq:.0f} Hz")
    
    print(f"\n⏱️ 3. 观测时长 (T = 10 ms)")
    print("-" * 50)
    sequence_length = int(T * F_S)
    print(f"   物理意义: 单次目标观测的时间窗口")
    print(f"   序列长度 = T × F_S = {sequence_length:,} 个采样点")
    print(f"   影响因素:")
    print(f"   • 频率分辨率: Δf = 1/T = {1/T:.0f} Hz")
    print(f"   • 目标运动: 观测时间内目标位移和速度变化")
    print(f"   • 信噪比: 观测时间越长，信号积累效果越好")
    print(f"   • 实时性: 观测时间影响系统响应速度")
    
    print(f"\n🌊 4. 电磁波传播特性")
    print("-" * 50)
    print(f"   光速 c = {c:.0e} m/s")
    print(f"   自由空间传播损耗公式: L = (4πR/λ)²")
    print(f"   其中 R 为传播距离，λ 为波长")
    
    # 计算不同距离的传播损耗
    distances = np.array([500, 1000, 1500, 2000])
    path_loss_linear = (4 * np.pi * distances / wavelength) ** 2
    path_loss_dB = 10 * np.log10(path_loss_linear)
    
    print(f"   传播损耗示例:")
    for dist, loss_dB in zip(distances, path_loss_dB):
        print(f"   • {dist:4d}米: {loss_dB:.1f} dB")
    
    print(f"\n🎯 5. 多普勒效应分析")
    print("-" * 50)
    print(f"   多普勒频移公式: f_d = (v_r/c) × f_c")
    print(f"   其中 v_r 为径向速度，正值表示接近，负值表示远离")
    
    # 计算不同速度的多普勒频移
    velocities = np.array([-100, -50, 0, 50, 100])  # m/s
    doppler_shifts = (velocities / c) * F_C
    
    print(f"   多普勒频移示例 (载波频率 {F_C/1e9:.1f} GHz):")
    for vel, doppler in zip(velocities, doppler_shifts):
        direction = "接近" if vel > 0 else "远离" if vel < 0 else "静止"
        print(f"   • {vel:4d} m/s ({direction:2s}): {doppler:+6.2f} Hz")
    
    print(f"\n📐 6. 几何配置分析")
    print("-" * 50)
    p_receivers = np.array([
        [-1000.0, -1000.0, 0.0],
        [1000.0, -1000.0, 0.0],
        [1000.0, 1000.0, 0.0],
        [-1000.0, 1000.0, 0.0]
    ])
    
    print(f"   接收站布局: 正方形阵列")
    print(f"   阵列边长: 2000 米")
    print(f"   基线长度:")
    
    # 计算基线长度
    baselines = []
    for i in range(len(p_receivers)):
        for j in range(i+1, len(p_receivers)):
            baseline = np.linalg.norm(p_receivers[i] - p_receivers[j])
            baselines.append(baseline)
            print(f"   • 接收站{i+1}-接收站{j+1}: {baseline:.0f} 米")
    
    print(f"   几何精度因子 (GDOP) 影响:")
    print(f"   • 正方形布局提供良好的几何多样性")
    print(f"   • 基线长度影响定位精度")
    print(f"   • 目标在阵列中心时精度最高")
    
    print(f"\n🔢 7. 数据维度分析")
    print("-" * 50)
    n_receivers = 4
    n_iq_components = 2
    feature_dim = n_receivers * n_iq_components
    
    print(f"   输入特征维度: {feature_dim} 维")
    print(f"   • 接收站数量: {n_receivers} 个")
    print(f"   • IQ分量: {n_iq_components} 个 (I同相, Q正交)")
    print(f"   • 每个时间点特征: [{n_receivers}站] × [I,Q] = {feature_dim}维")
    print(f"   ")
    print(f"   时间序列长度: {sequence_length:,} 个时间点")
    print(f"   单个样本数据量: {sequence_length:,} × {feature_dim} = {sequence_length * feature_dim:,} 个数值")
    print(f"   ")
    print(f"   输出标签维度: 3 维 (x, y, z坐标)")
    
    print(f"\n⚡ 8. 信号处理链路")
    print("-" * 50)
    print(f"   信号处理流程:")
    print(f"   1️⃣ 目标反射 → 回波信号")
    print(f"   2️⃣ 传播延迟 → τ = R/c")
    print(f"   3️⃣ 多普勒频移 → f_d = (v_r/c)×f_c")
    print(f"   4️⃣ 路径损耗 → A = 1/R (简化模型)")
    print(f"   5️⃣ IQ调制 → I = A×cos(φ), Q = A×sin(φ)")
    print(f"   6️⃣ 噪声添加 → SNR控制")
    print(f"   7️⃣ 数字采样 → 离散化")
    
    print(f"\n🎛️ 9. 系统性能指标")
    print("-" * 50)
    
    # 理论性能计算
    max_range = 2000  # 最大探测距离
    range_resolution = c / (2 * F_S)  # 距离分辨率
    velocity_resolution = wavelength / (2 * T)  # 速度分辨率
    
    print(f"   理论性能指标:")
    print(f"   • 最大探测距离: ~{max_range} 米")
    print(f"   • 距离分辨率: c/(2×F_S) = {range_resolution:.0f} 米")
    print(f"   • 速度分辨率: λ/(2×T) = {velocity_resolution:.1f} m/s")
    print(f"   • 最大不模糊速度: λ×F_S/4 = {wavelength * F_S / 4:.0f} m/s")
    
    print(f"\n🔧 10. 参数优化建议")
    print("-" * 50)
    print(f"   提高定位精度的方法:")
    print(f"   • 增加载波频率 → 提高多普勒敏感性")
    print(f"   • 增加采样频率 → 提高时间分辨率")
    print(f"   • 延长观测时间 → 提高频率分辨率")
    print(f"   • 优化接收站布局 → 改善几何精度因子")
    print(f"   • 提高信噪比 → 减少测量误差")
    print(f"   • 增加接收站数量 → 提供冗余信息")

def create_parameter_impact_visualization():
    """创建参数影响可视化图"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 载波频率对多普勒敏感性的影响
    ax1 = axes[0, 0]
    frequencies = np.array([0.5, 1.0, 2.0, 5.0, 10.0])  # GHz
    velocity = 50  # m/s
    c = 3e8
    doppler_shifts = (velocity / c) * frequencies * 1e9
    
    ax1.bar(frequencies, doppler_shifts, alpha=0.7, color='skyblue')
    ax1.set_xlabel('载波频率 (GHz)')
    ax1.set_ylabel('多普勒频移 (Hz)')
    ax1.set_title('载波频率对多普勒敏感性的影响\n(目标速度: 50 m/s)')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标注
    for freq, doppler in zip(frequencies, doppler_shifts):
        ax1.text(freq, doppler + 5, f'{doppler:.1f}', ha='center', fontweight='bold')
    
    # 2. 采样频率对时间分辨率的影响
    ax2 = axes[0, 1]
    sampling_freqs = np.array([0.5, 1.0, 2.0, 5.0, 10.0])  # MHz
    time_resolutions = 1 / (sampling_freqs * 1e6) * 1e6  # 微秒
    
    ax2.bar(sampling_freqs, time_resolutions, alpha=0.7, color='lightcoral')
    ax2.set_xlabel('采样频率 (MHz)')
    ax2.set_ylabel('时间分辨率 (微秒)')
    ax2.set_title('采样频率对时间分辨率的影响')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标注
    for freq, res in zip(sampling_freqs, time_resolutions):
        ax2.text(freq, res + 0.1, f'{res:.1f}', ha='center', fontweight='bold')
    
    # 3. 观测时间对频率分辨率的影响
    ax3 = axes[1, 0]
    observation_times = np.array([1, 5, 10, 20, 50])  # ms
    freq_resolutions = 1000 / observation_times  # Hz
    
    ax3.bar(observation_times, freq_resolutions, alpha=0.7, color='lightgreen')
    ax3.set_xlabel('观测时间 (ms)')
    ax3.set_ylabel('频率分辨率 (Hz)')
    ax3.set_title('观测时间对频率分辨率的影响')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标注
    for time, res in zip(observation_times, freq_resolutions):
        ax3.text(time, res + 5, f'{res:.0f}', ha='center', fontweight='bold')
    
    # 4. 距离对信号强度的影响
    ax4 = axes[1, 1]
    distances = np.linspace(500, 2500, 100)
    wavelength = 0.3  # 1GHz对应波长
    path_loss_dB = 20 * np.log10(4 * np.pi * distances / wavelength)
    
    ax4.plot(distances, path_loss_dB, linewidth=3, color='purple')
    ax4.set_xlabel('距离 (米)')
    ax4.set_ylabel('路径损耗 (dB)')
    ax4.set_title('距离对信号强度的影响\n(自由空间传播损耗)')
    ax4.grid(True, alpha=0.3)
    
    # 标注关键点
    key_distances = [1000, 1500, 2000]
    for dist in key_distances:
        loss = 20 * np.log10(4 * np.pi * dist / wavelength)
        ax4.plot(dist, loss, 'ro', markersize=8)
        ax4.annotate(f'{dist}m\n{loss:.0f}dB', (dist, loss), 
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('parameter_impact_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("开始参数详细分析...")
    analyze_system_parameters()
    print("\n" + "=" * 100)
    print("生成参数影响可视化图...")
    create_parameter_impact_visualization()
    print("分析完成！参数影响图已保存为 'parameter_impact_analysis.png'")
