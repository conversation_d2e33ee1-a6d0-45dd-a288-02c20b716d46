import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Input, GRU
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# -----------------------------------------------------------------------------
# 1. 加载并预处理数据
# -----------------------------------------------------------------------------
print("加载数据...")
# 假设你已经运行了上面的生成函数
data_path = r"D:\code\emitter location\training_data"
X_raw = np.load('training_data/X_data.npy')
y_raw = np.load('training_data/y_data.npy')

# -- 数据预处理 --
# 形状: (n_samples, n_timesteps, n_features)
n_samples, n_timesteps, n_features = X_raw.shape

# 将数据reshape成2D以进行标准化
X_reshaped = X_raw.reshape(-1, n_features)

# 标准化输入特征 (非常重要!)
scaler_X = StandardScaler()
X_scaled_reshaped = scaler_X.fit_transform(X_reshaped)

# 将数据恢复成3D形状
X_scaled = X_scaled_reshaped.reshape(n_samples, n_timesteps, n_features)

# (可选) 标准化输出标签
# scaler_y = StandardScaler()
# y_scaled = scaler_y.fit_transform(y_raw)
# 在这个案例中，我们先不标准化输出，直接预测坐标

# 划分训练集、验证集和测试集
X_train, X_temp, y_train, y_temp = train_test_split(X_scaled, y_raw, test_size=0.3, random_state=42)
X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42)

print(f"训练集大小: {X_train.shape}")
print(f"验证集大小: {X_val.shape}")
print(f"测试集大小: {X_test.shape}")

# -----------------------------------------------------------------------------
# 2. 构建RNN模型
# -----------------------------------------------------------------------------
def build_model(input_shape):
    model = Sequential([
        Input(shape=input_shape),
        # 使用GRU或LSTM。GRU参数更少，训练更快
        # return_sequences=False 表示只输出序列的最后一个时间步的隐藏状态
        GRU(64, return_sequences=False),
        Dense(128, activation='relu'),
        Dense(64, activation='relu'),
        # 输出层有3个神经元，对应x, y, z坐标，没有激活函数（线性输出）
        Dense(3)
    ])
    return model

model = build_model((n_timesteps, n_features))
model.summary()

# -----------------------------------------------------------------------------
# 3. 编译和训练模型
# -----------------------------------------------------------------------------
# 对于回归问题，使用均方误差(MSE)作为损失函数
model.compile(optimizer='adam', loss='mean_squared_error')

print("\n开始训练模型...")
history = model.fit(
    X_train, y_train,
    epochs=50,  # 初始可以设置少一点
    batch_size=32,
    validation_data=(X_val, y_val),
    callbacks=[
        tf.keras.callbacks.EarlyStopping(monitor='val_loss', patience=5, restore_best_weights=True)
    ]
)

# -----------------------------------------------------------------------------
# 4. 评估模型
# -----------------------------------------------------------------------------
print("\n评估模型性能...")
test_loss = model.evaluate(X_test, y_test)
print(f"测试集上的均方误差 (MSE): {test_loss:.2f}")

# 进行预测
y_pred = model.predict(X_test)

# 计算一个更直观的指标：平均欧几里得距离误差 (米)
euclidean_errors = np.linalg.norm(y_test - y_pred, axis=1)
mean_error = np.mean(euclidean_errors)
median_error = np.median(euclidean_errors)

print(f"测试集上的平均定位误差 (米): {mean_error:.2f} m")
print(f"测试集上的定位误差中位数 (米): {median_error:.2f} m")

# 可视化训练过程
plt.figure(figsize=(10, 5))
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss During Training')
plt.xlabel('Epoch')
plt.ylabel('Mean Squared Error')
plt.legend()
plt.grid(True)
plt.show()

# 可视化误差分布
plt.figure(figsize=(10, 5))
plt.hist(euclidean_errors, bins=50, alpha=0.7)
plt.title('Distribution of Localization Errors')
plt.xlabel('Euclidean Error (m)')
plt.ylabel('Frequency')
plt.grid(True)
plt.show()