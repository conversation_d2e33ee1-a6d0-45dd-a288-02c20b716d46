"""
按照用户要求的图例样式修改的可视化脚本
图例显示在图的右上角，采用框式布局
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.patches import FancyBboxPatch, Rectangle
import matplotlib.patches as mpatches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def visualize_with_legend_box():
    """带有框式图例的雷达定位场景可视化"""
    
    # 系统参数
    c = 3e8  # 光速 (m/s)
    F_C = 1e9  # 载波频率 (1 GHz)
    F_S = 1e6  # 采样频率 (1 MHz)
    T = 0.01  # 仿真时长 (s)
    
    # 接收站位置
    p_receivers = np.array([
        [-1000.0, -1000.0, 0.0],
        [1000.0, -1000.0, 0.0],
        [1000.0, 1000.0, 0.0],
        [-1000.0, 1000.0, 0.0]
    ])
    
    # 示例目标位置
    p_target = np.array([200.0, 300.0, 500.0])
    
    # 创建图形
    fig = plt.figure(figsize=(18, 12))
    
    # 1. 3D场景图
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # 绘制接收站
    ax1.scatter(p_receivers[:, 0], p_receivers[:, 1], p_receivers[:, 2], 
               c='red', s=300, marker='^', edgecolors='black', linewidth=2)
    
    # 标注接收站
    station_names = ['接收站1', '接收站2', '接收站3', '接收站4']
    for i, (pos, name) in enumerate(zip(p_receivers, station_names)):
        ax1.text(pos[0], pos[1], pos[2]+150, name, fontsize=12, ha='center', 
                fontweight='bold', bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    # 绘制示例目标位置
    ax1.scatter(p_target[0], p_target[1], p_target[2], 
               c='blue', s=200, marker='o', edgecolors='black', linewidth=2)
    
    # 绘制信号传播路径
    for i, rx_pos in enumerate(p_receivers):
        ax1.plot([p_target[0], rx_pos[0]], 
                [p_target[1], rx_pos[1]], 
                [p_target[2], rx_pos[2]], 
                'gray', linestyle='--', alpha=0.6, linewidth=2)
    
    ax1.set_xlabel('X坐标 (米)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Y坐标 (米)', fontsize=14, fontweight='bold')
    ax1.set_zlabel('Z坐标 (米)', fontsize=14, fontweight='bold')
    ax1.set_title('雷达定位系统3D场景图', fontsize=16, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 添加自定义图例框 - 3D图
    legend_elements_3d = [
        mpatches.Patch(color='red', label='▲ 接收站'),
        mpatches.Patch(color='blue', label='● 示例目标'),
        mpatches.Patch(color='gray', label='--- 信号传播路径')
    ]
    legend_3d = ax1.legend(handles=legend_elements_3d, loc='upper right',
                          bbox_to_anchor=(1.15, 1), fontsize=11,
                          frameon=True, fancybox=True, shadow=True)
    legend_3d.get_frame().set_facecolor('white')
    legend_3d.get_frame().set_edgecolor('black')
    legend_3d.get_frame().set_linewidth(1)
    
    # 2. 俯视图
    ax2 = fig.add_subplot(2, 3, 2)
    ax2.scatter(p_receivers[:, 0], p_receivers[:, 1], c='red', s=300, marker='^', 
               edgecolors='black', linewidth=2)
    ax2.scatter(p_target[0], p_target[1], c='blue', s=200, marker='o', 
               edgecolors='black', linewidth=2)
    
    # 标注接收站编号
    for i, pos in enumerate(p_receivers):
        ax2.annotate(f'RX{i+1}', (pos[0], pos[1]), xytext=(15, 15), 
                    textcoords='offset points', fontsize=14, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
    
    # 绘制覆盖范围
    circle = plt.Circle((0, 0), 1500, fill=False, linestyle=':', color='gray', alpha=0.8, linewidth=3)
    ax2.add_patch(circle)
    ax2.text(0, 1650, '系统覆盖范围\n(半径1500m)', ha='center', fontsize=12, fontweight='bold',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
    
    # 绘制基线
    for i in range(len(p_receivers)):
        for j in range(i+1, len(p_receivers)):
            ax2.plot([p_receivers[i, 0], p_receivers[j, 0]], 
                    [p_receivers[i, 1], p_receivers[j, 1]], 
                    'k-', alpha=0.3, linewidth=1)
    
    ax2.set_xlabel('X坐标 (米)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Y坐标 (米)', fontsize=14, fontweight='bold')
    ax2.set_title('系统俯视图', fontsize=16, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.axis('equal')
    
    # 添加自定义图例框 - 俯视图
    legend_elements_2d = [
        mpatches.Patch(color='red', label='▲ 接收站'),
        mpatches.Patch(color='blue', label='■ 目标轨迹区(示例)')
    ]
    legend_2d = ax2.legend(handles=legend_elements_2d, loc='upper right',
                          bbox_to_anchor=(1.15, 1), fontsize=11,
                          frameon=True, fancybox=True, shadow=True)
    legend_2d.get_frame().set_facecolor('white')
    legend_2d.get_frame().set_edgecolor('black')
    legend_2d.get_frame().set_linewidth(1)
    
    # 3. 系统参数表
    ax3 = fig.add_subplot(2, 3, 3)
    ax3.axis('off')
    
    param_data = [
        ['参数名称', '数值', '单位', '说明'],
        ['载波频率', f'{F_C/1e9:.1f}', 'GHz', '雷达工作频率'],
        ['采样频率', f'{F_S/1e6:.1f}', 'MHz', 'IQ信号采样率'],
        ['观测时长', f'{T*1000:.1f}', 'ms', '单次观测时间'],
        ['接收站数量', '4', '个', '多站定位系统'],
        ['序列长度', f'{int(T * F_S):,}', '点', '时间采样点数'],
        ['特征维度', '8', '维', '4站×2(I/Q)'],
        ['波长', f'{c/F_C:.2f}', 'm', '电磁波波长'],
        ['目标范围', '±800', 'm', 'XY平面范围'],
        ['高度范围', '100-800', 'm', 'Z轴范围'],
        ['速度范围', '±100', 'm/s', '目标速度范围']
    ]
    
    table = ax3.table(cellText=param_data[1:], colLabels=param_data[0],
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(11)
    table.scale(1, 2.2)
    
    # 设置表格样式
    for i in range(len(param_data)):
        for j in range(len(param_data[0])):
            cell = table[(i, j)]
            if i == 0:  # 表头
                cell.set_facecolor('#2E7D32')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#E8F5E8' if i % 2 == 0 else 'white')
                cell.set_text_props(weight='bold' if j == 0 else 'normal')
    
    ax3.set_title('系统参数表', fontsize=16, fontweight='bold', pad=20)
    
    # 4. 信号处理流程图
    ax4 = fig.add_subplot(2, 3, 4)
    ax4.axis('off')
    
    steps = [
        '目标运动',
        '信号传播',
        '多普勒效应',
        '路径损耗',
        'IQ调制',
        '噪声添加',
        '数据采集'
    ]
    
    descriptions = [
        '位置+速度',
        '距离+延迟',
        '频率偏移',
        '幅度衰减',
        '复数信号',
        '高斯噪声',
        '4站×IQ'
    ]
    
    y_positions = np.linspace(0.9, 0.1, len(steps))
    box_width = 0.7
    box_height = 0.08
    
    colors = ['#FF9800', '#2196F3', '#2196F3', '#2196F3', '#2196F3', '#2196F3', '#4CAF50']
    
    for i, (step, desc, y_pos, color) in enumerate(zip(steps, descriptions, y_positions, colors)):
        # 绘制流程框
        box = FancyBboxPatch((0.15, y_pos - box_height/2), box_width, box_height,
                           boxstyle="round,pad=0.01", facecolor=color, alpha=0.8,
                           edgecolor='black', linewidth=1)
        ax4.add_patch(box)
        
        # 添加文字
        ax4.text(0.5, y_pos + 0.01, step, ha='center', va='center', fontsize=12, 
                fontweight='bold', color='white')
        ax4.text(0.5, y_pos - 0.02, f'({desc})', ha='center', va='center', fontsize=10, 
                color='white')
        
        # 添加箭头
        if i < len(steps) - 1:
            ax4.arrow(0.5, y_pos - box_height/2 - 0.01, 0, -0.03, 
                     head_width=0.04, head_length=0.015, fc='black', ec='black')
    
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.set_title('信号处理流程', fontsize=16, fontweight='bold')
    
    # 5. 几何配置分析
    ax5 = fig.add_subplot(2, 3, 5)
    
    # 计算基线长度
    baseline_lengths = []
    baseline_names = []
    for i in range(len(p_receivers)):
        for j in range(i+1, len(p_receivers)):
            length = np.linalg.norm(p_receivers[i] - p_receivers[j])
            baseline_lengths.append(length)
            baseline_names.append(f'RX{i+1}-RX{j+1}')
    
    bars = ax5.bar(range(len(baseline_lengths)), baseline_lengths, 
                   color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
                   alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加数值标注
    for bar, length in zip(bars, baseline_lengths):
        height = bar.get_height()
        ax5.text(bar.get_x() + bar.get_width()/2., height + 20,
                f'{length:.0f}m', ha='center', va='bottom', fontweight='bold')
    
    ax5.set_xlabel('基线', fontsize=14, fontweight='bold')
    ax5.set_ylabel('长度 (米)', fontsize=14, fontweight='bold')
    ax5.set_title('接收站基线长度', fontsize=16, fontweight='bold')
    ax5.set_xticks(range(len(baseline_names)))
    ax5.set_xticklabels(baseline_names, rotation=45)
    ax5.grid(True, alpha=0.3)
    
    # 6. 多普勒效应示例
    ax6 = fig.add_subplot(2, 3, 6)
    
    # 不同速度的多普勒频移
    velocities = np.array([-100, -50, -25, 0, 25, 50, 100])
    doppler_shifts = (velocities / c) * F_C
    
    colors = ['red' if v < 0 else 'blue' if v > 0 else 'gray' for v in velocities]
    bars = ax6.bar(range(len(velocities)), doppler_shifts, color=colors, alpha=0.7,
                   edgecolor='black', linewidth=1)
    
    # 添加数值标注
    for i, (bar, doppler) in enumerate(zip(bars, doppler_shifts)):
        height = bar.get_height()
        ax6.text(bar.get_x() + bar.get_width()/2., 
                height + (5 if height >= 0 else -15),
                f'{doppler:.1f}Hz', ha='center', 
                va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    ax6.set_xlabel('径向速度 (m/s)', fontsize=14, fontweight='bold')
    ax6.set_ylabel('多普勒频移 (Hz)', fontsize=14, fontweight='bold')
    ax6.set_title('多普勒效应示例\n(载波频率1GHz)', fontsize=16, fontweight='bold')
    ax6.set_xticks(range(len(velocities)))
    ax6.set_xticklabels(velocities)
    ax6.grid(True, alpha=0.3)
    ax6.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 添加图例框 - 多普勒图
    legend_elements_doppler = [
        mpatches.Patch(color='red', label='远离'),
        mpatches.Patch(color='blue', label='接近'),
        mpatches.Patch(color='gray', label='静止')
    ]
    legend_doppler = ax6.legend(handles=legend_elements_doppler, loc='upper right',
                               bbox_to_anchor=(1.15, 1), fontsize=11,
                               frameon=True, fancybox=True, shadow=True)
    legend_doppler.get_frame().set_facecolor('white')
    legend_doppler.get_frame().set_edgecolor('black')
    legend_doppler.get_frame().set_linewidth(1)
    
    plt.tight_layout()
    plt.savefig('radar_scenario_with_legend_box.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印系统配置信息
    print("=" * 80)
    print("雷达定位系统配置信息")
    print("=" * 80)
    
    print(f"\n📡 系统参数:")
    print(f"   载波频率: {F_C/1e9:.1f} GHz (波长: {c/F_C:.2f} 米)")
    print(f"   采样频率: {F_S/1e6:.1f} MHz (采样间隔: {1/F_S*1e6:.1f} 微秒)")
    print(f"   观测时长: {T*1000:.1f} 毫秒 ({int(T*F_S):,} 个采样点)")
    
    print(f"\n📐 几何配置:")
    print(f"   接收站布局: 正方形阵列，边长2000米")
    for i, pos in enumerate(p_receivers):
        print(f"   接收站{i+1}: ({pos[0]:6.0f}, {pos[1]:6.0f}, {pos[2]:3.0f}) 米")
    
    print(f"\n📊 基线长度:")
    for name, length in zip(baseline_names, baseline_lengths):
        print(f"   {name}: {length:.0f} 米")
    
    print(f"\n🎯 示例目标:")
    print(f"   位置: ({p_target[0]:.0f}, {p_target[1]:.0f}, {p_target[2]:.0f}) 米")
    for i, rx_pos in enumerate(p_receivers):
        dist = np.linalg.norm(p_target - rx_pos)
        print(f"   到接收站{i+1}距离: {dist:.1f} 米")

if __name__ == "__main__":
    print("开始生成带框式图例的IQ数据场景可视化...")
    visualize_with_legend_box()
    print("\n带框式图例的可视化完成！图像已保存为 'radar_scenario_with_legend_box.png'")
