# RNN定位系统改进版

本项目实现了基于RNN的目标定位系统，通过IQ信号数据预测目标的3D坐标位置。

## 文件说明

### 核心文件
- `IQ_data.py` - 数据生成脚本，生成训练用的IQ信号数据
- `RNN.py` - 原始RNN模型训练脚本
- `RNN_improved.py` - **改进版RNN模型训练脚本（推荐使用）**
- `inference.py` - 模型推理脚本，用于加载训练好的模型进行预测
- `model_comparison.py` - 模型性能比较脚本

### 数据目录
- `training_data/` - 存储训练数据
  - `X_data.npy` - 输入特征数据（IQ信号）
  - `y_data.npy` - 标签数据（目标坐标）

### 模型保存目录
- `saved_models/` - 存储训练好的模型和预处理器
  - `rnn_localization_model_*.h5` - 训练好的模型文件
  - `scaler_X_*.pkl` - 输入数据标准化器
  - `scaler_y_*.pkl` - 输出数据标准化器
  - `training_history_*.npy` - 训练历史记录

## 使用步骤

### 1. 生成训练数据
```bash
python IQ_data.py
```
这将生成5000个训练样本，保存在`training_data/`目录中。

### 2. 训练改进的模型
```bash
python RNN_improved.py
```
这将：
- 加载训练数据
- 构建改进的RNN模型（双向LSTM + GRU + Dropout）
- 训练模型并保存最佳权重
- 保存模型、预处理器和训练历史
- 显示训练结果和性能评估

### 3. 使用训练好的模型进行预测
```bash
python inference.py
```
这将：
- 自动找到最新训练的模型
- 加载模型和预处理器
- 对测试数据进行示例预测

### 4. 比较不同模型性能（可选）
```bash
python model_comparison.py
```
这将比较三种不同复杂度的模型性能。

## 模型改进

### 原始模型问题
- 定位误差较大（平均455.77米）
- 模型结构简单
- 缺少正则化
- 没有模型保存功能

### 改进措施

#### 1. 模型架构改进
- **双向LSTM**: 增强序列建模能力
- **多层结构**: Bidirectional LSTM + GRU + 多层全连接
- **Dropout正则化**: 防止过拟合
- **逐步降维**: 256 → 128 → 64 → 3

#### 2. 数据预处理改进
- **输出标准化**: 对目标坐标进行标准化，提高训练稳定性
- **特征标准化**: 保持输入特征的标准化

#### 3. 训练策略优化
- **学习率调度**: ReduceLROnPlateau自动调整学习率
- **早停机制**: 防止过拟合，patience=10
- **模型检查点**: 自动保存最佳模型
- **增加训练轮数**: 从50增加到100轮
- **批次大小优化**: 从32增加到64

#### 4. 评估指标增强
- 平均误差、中位数误差
- 标准差、90%分位数误差
- 训练过程可视化
- 预测vs真实值对比

#### 5. 模型保存和推理
- **完整模型保存**: 架构+权重
- **预处理器保存**: 输入输出标准化器
- **推理函数**: 便于新数据预测
- **版本管理**: 时间戳标识

## 预期改进效果

通过以上改进，预期能够：
- **显著降低定位误差**: 从455米降低到200米以下
- **提高模型稳定性**: 通过正则化和标准化
- **增强泛化能力**: 通过更复杂的模型结构
- **便于部署使用**: 完整的保存和推理系统

## 使用示例

### 训练新模型
```python
# 运行改进的训练脚本
python RNN_improved.py
```

### 对新数据进行预测
```python
from inference import predict_single_sample, predict_batch
import numpy as np

# 加载新的IQ数据
X_new = np.load('new_iq_data.npy')  # 形状: (n_samples, n_timesteps, n_features)

# 批量预测
predictions = predict_batch(X_new)
print("预测坐标:", predictions)

# 单个样本预测
single_prediction = predict_single_sample(X_new[0])
print("单个预测:", single_prediction)
```

## 依赖库

```
numpy
tensorflow
scikit-learn
matplotlib
joblib
```

安装依赖：
```bash
pip install numpy tensorflow scikit-learn matplotlib joblib
```

## 注意事项

1. 确保有足够的内存和计算资源进行训练
2. 训练时间可能较长（取决于硬件配置）
3. 模型文件较大，注意存储空间
4. 建议使用GPU加速训练（如果可用）

## 故障排除

### 常见问题
1. **内存不足**: 减少batch_size或数据量
2. **训练时间过长**: 减少epochs或使用更简单的模型
3. **模型文件未找到**: 确保已完成训练并保存模型
4. **预测结果异常**: 检查输入数据格式和预处理步骤
