import numpy as np
import os

def generate_dataset(num_samples, output_dir='training_data'):
    """
    生成包含多个随机场景的训练数据集。
    """
    # 检查并创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print(f"开始生成 {num_samples} 个训练样本到: {output_dir}")
    
    # --- 固定参数 ---
    c = 3e8  # 光速 (m/s)
    F_C = 1e9       # 载波频率 (1 GHz)
    F_S = 1e6       # 采样频率 (1 MHz)
    T = 0.01        # 每个样本的仿真时长 (s)
    N_SAMPLES = int(T * F_S) # 每个样本的序列长度

    # 接收站位置 (保持固定)
    p_receivers = np.array([
        [-1000.0, -1000.0, 0.0],
        [1000.0, -1000.0, 0.0],
        [1000.0, 1000.0, 0.0],
        [-1000.0, 1000.0, 0.0]
    ])
    N_RX = len(p_receivers)

    # --- 数据集容器 ---
    # 输入 X: (样本数, 序列长度, 特征数) -> 特征数 = 4个接收站 * 2 (I/Q) = 8
    X_data = np.zeros((num_samples, N_SAMPLES, N_RX * 2)) 
    # 标签 y: (样本数, 坐标xyz)
    y_data = np.zeros((num_samples, 3))
    
    # 定义时间轴 (对于所有样本都相同)
    t_axis = np.linspace(0, T, N_SAMPLES, endpoint=False)

    for i in range(num_samples):
        # --- 在每个样本循环开始时进行随机化 ---
        # 随机信噪比，增加模型鲁棒性
        SNR_dB = np.random.uniform(10, 25)
        # 随机化目标的初始位置
        p_target_initial = np.random.uniform(low=-800, high=800, size=3)
        p_target_initial[2] = np.random.uniform(low=100, high=800) # 确保Z轴(高度)为正
        # 随机化目标的速度矢量
        v_target = np.random.uniform(low=-100, high=100, size=3)

        # 样本的标签是序列结束时刻的最终位置
        p_target_final = p_target_initial + v_target * T
        y_data[i] = p_target_final


        displacement = t_axis[:, np.newaxis] * v_target
        p_target_t = p_target_initial + displacement
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        all_rx_iq = []
        for rx_idx in range(N_RX):
            p_rx = p_receivers[rx_idx]
            
            # 计算每个时间点上，目标到当前接收站的矢量和距离
            vector_to_target = p_target_t - p_rx
            distance = np.linalg.norm(vector_to_target, axis=1) # axis=1 表示沿xyz坐标轴计算范数
            
            # 计算多普勒频移
            # 径向速度是速度矢量在视线方向上的投影
            v_radial = np.sum(v_target * vector_to_target, axis=1) / distance
            f_doppler = (v_radial / c) * F_C
            
            # 计算传播延迟
            tau = distance / c
            
            # 计算信号幅度和相位
            amplitude = 1.0 / distance # 简化路径损耗模型
            phase = 2 * np.pi * (F_C + f_doppler) * (t_axis - tau)
            
            # 生成无噪声IQ信号
            noiseless_signal = amplitude * np.exp(1j * phase)
            
            # 计算噪声功率并添加高斯白噪声
            signal_power = np.mean(np.abs(noiseless_signal)**2)
            noise_power = signal_power / (10**(SNR_dB / 10))
            noise = np.random.normal(0, np.sqrt(noise_power / 2), N_SAMPLES) + \
                    1j * np.random.normal(0, np.sqrt(noise_power / 2), N_SAMPLES)
            
            all_rx_iq.append(noiseless_signal + noise)
        
        # 将4个接收站的IQ数据拼接成 (N_SAMPLES, 8) 的矩阵
        # 格式: [I1, Q1, I2, Q2, I3, Q3, I4, Q4]
        sample_iq_matrix = np.zeros((N_SAMPLES, N_RX * 2))
        for rx_idx in range(N_RX):
            sample_iq_matrix[:, 2*rx_idx] = np.real(all_rx_iq[rx_idx])
            sample_iq_matrix[:, 2*rx_idx + 1] = np.imag(all_rx_iq[rx_idx])

        X_data[i] = sample_iq_matrix
        
        # 打印进度
        if (i+1) % 100 == 0:
            print(f"已生成 {i+1}/{num_samples} 个样本...")

    # --- 保存数据集 ---
    np.save(os.path.join(output_dir, 'X_data.npy'), X_data)
    np.save(os.path.join(output_dir, 'y_data.npy'), y_data)
    print("数据集生成完毕并已保存。")
    return X_data, y_data


# --- 脚本执行入口 ---

if __name__ == '__main__':
    # 定义你的文件路径 (使用原始字符串 r"..." 是最简单的方法)
    my_output_path = r"D:\code\emitter location\training_data"

    # 调用函数，生成5000个样本
    generate_dataset(5000, output_dir=my_output_path)