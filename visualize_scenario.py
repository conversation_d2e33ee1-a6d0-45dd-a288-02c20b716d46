"""
IQ数据场景可视化脚本
详细展示雷达定位系统的几何配置和信号传播过程
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def visualize_radar_scenario():
    """可视化雷达定位场景"""
    
    # 系统参数（来自IQ_data.py）
    c = 3e8  # 光速 (m/s)
    F_C = 1e9  # 载波频率 (1 GHz)
    F_S = 1e6  # 采样频率 (1 MHz)
    T = 0.01  # 仿真时长 (s)
    
    # 接收站位置
    p_receivers = np.array([
        [-1000.0, -1000.0, 0.0],
        [1000.0, -1000.0, 0.0],
        [1000.0, 1000.0, 0.0],
        [-1000.0, 1000.0, 0.0]
    ])
    
    # 示例目标轨迹
    p_target_initial = np.array([200.0, 300.0, 500.0])
    v_target = np.array([50.0, -30.0, 20.0])
    
    # 创建图形
    fig = plt.figure(figsize=(20, 15))
    
    # 3D场景图
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    # 绘制接收站
    ax1.scatter(p_receivers[:, 0], p_receivers[:, 1], p_receivers[:, 2], 
               c='red', s=200, marker='^', label='接收站')
    
    # 标注接收站
    station_names = ['接收站1\n(-1000,-1000,0)', '接收站2\n(1000,-1000,0)', 
                    '接收站3\n(1000,1000,0)', '接收站4\n(-1000,1000,0)']
    for i, (pos, name) in enumerate(zip(p_receivers, station_names)):
        ax1.text(pos[0], pos[1], pos[2]+100, name, fontsize=10, ha='center')
    
    # 绘制目标轨迹
    t_points = np.linspace(0, T, 20)
    trajectory = p_target_initial + np.outer(t_points, v_target)
    
    ax1.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2], 
            'b-', linewidth=3, label='目标轨迹')
    ax1.scatter(p_target_initial[0], p_target_initial[1], p_target_initial[2], 
               c='blue', s=150, marker='o', label='初始位置')
    ax1.scatter(trajectory[-1, 0], trajectory[-1, 1], trajectory[-1, 2], 
               c='green', s=150, marker='s', label='最终位置')
    
    # 绘制信号传播路径（从目标到各接收站）
    for i, rx_pos in enumerate(p_receivers):
        ax1.plot([p_target_initial[0], rx_pos[0]], 
                [p_target_initial[1], rx_pos[1]], 
                [p_target_initial[2], rx_pos[2]], 
                'gray', linestyle='--', alpha=0.5)
    
    ax1.set_xlabel('X坐标 (米)', fontsize=12)
    ax1.set_ylabel('Y坐标 (米)', fontsize=12)
    ax1.set_zlabel('Z坐标 (米)', fontsize=12)
    ax1.set_title('雷达定位系统3D场景图', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True)
    
    # 俯视图
    ax2 = fig.add_subplot(2, 3, 2)
    ax2.scatter(p_receivers[:, 0], p_receivers[:, 1], c='red', s=200, marker='^', label='接收站')
    ax2.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=3, label='目标轨迹(俯视)')
    ax2.scatter(p_target_initial[0], p_target_initial[1], c='blue', s=150, marker='o')
    ax2.scatter(trajectory[-1, 0], trajectory[-1, 1], c='green', s=150, marker='s')
    
    # 标注接收站编号
    for i, pos in enumerate(p_receivers):
        ax2.annotate(f'RX{i+1}', (pos[0], pos[1]), xytext=(10, 10), 
                    textcoords='offset points', fontsize=12, fontweight='bold')
    
    # 绘制覆盖范围
    circle = plt.Circle((0, 0), 1500, fill=False, linestyle=':', color='gray', alpha=0.7)
    ax2.add_patch(circle)
    ax2.text(0, 1600, '系统覆盖范围\n(半径1500m)', ha='center', fontsize=10)
    
    ax2.set_xlabel('X坐标 (米)', fontsize=12)
    ax2.set_ylabel('Y坐标 (米)', fontsize=12)
    ax2.set_title('系统俯视图', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True)
    ax2.axis('equal')
    
    # 系统参数表
    ax3 = fig.add_subplot(2, 3, 3)
    ax3.axis('off')
    
    # 创建参数表格
    param_data = [
        ['参数名称', '数值', '单位', '说明'],
        ['载波频率 (F_C)', f'{F_C/1e9:.1f}', 'GHz', '雷达工作频率'],
        ['采样频率 (F_S)', f'{F_S/1e6:.1f}', 'MHz', 'IQ信号采样率'],
        ['仿真时长 (T)', f'{T*1000:.1f}', 'ms', '单次观测时间'],
        ['光速 (c)', f'{c/1e8:.1f}×10⁸', 'm/s', '电磁波传播速度'],
        ['接收站数量', '4', '个', '多站定位系统'],
        ['序列长度', f'{int(T * F_S)}', '点', '每个样本的时间点数'],
        ['特征维度', '8', '维', '4站×2(I/Q)'],
        ['目标范围', '±800', 'm', 'XY平面活动范围'],
        ['高度范围', '100-800', 'm', 'Z轴活动范围'],
        ['速度范围', '±100', 'm/s', '各轴向最大速度']
    ]
    
    # 绘制表格
    table = ax3.table(cellText=param_data[1:], colLabels=param_data[0],
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(param_data)):
        for j in range(len(param_data[0])):
            cell = table[(i, j)]
            if i == 0:  # 表头
                cell.set_facecolor('#4CAF50')
                cell.set_text_props(weight='bold', color='white')
            else:
                cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
    
    ax3.set_title('系统参数表', fontsize=14, fontweight='bold', pad=20)
    
    # 信号处理流程图
    ax4 = fig.add_subplot(2, 3, 4)
    ax4.axis('off')
    
    # 流程步骤
    steps = [
        '目标运动\n(位置+速度)',
        '信号传播\n(距离+延迟)',
        '多普勒效应\n(频率偏移)',
        '路径损耗\n(幅度衰减)',
        'IQ调制\n(复数信号)',
        '噪声添加\n(高斯白噪声)',
        '数据采集\n(4站×IQ)'
    ]
    
    # 绘制流程框
    y_positions = np.linspace(0.9, 0.1, len(steps))
    box_width = 0.8
    box_height = 0.08
    
    for i, (step, y_pos) in enumerate(zip(steps, y_positions)):
        # 绘制流程框
        if i == 0:
            color = '#FF9800'  # 橙色 - 输入
        elif i == len(steps) - 1:
            color = '#4CAF50'  # 绿色 - 输出
        else:
            color = '#2196F3'  # 蓝色 - 处理
            
        box = FancyBboxPatch((0.1, y_pos - box_height/2), box_width, box_height,
                           boxstyle="round,pad=0.01", facecolor=color, alpha=0.7)
        ax4.add_patch(box)
        
        # 添加文字
        ax4.text(0.5, y_pos, step, ha='center', va='center', fontsize=10, 
                fontweight='bold', color='white')
        
        # 添加箭头
        if i < len(steps) - 1:
            ax4.arrow(0.5, y_pos - box_height/2 - 0.01, 0, -0.03, 
                     head_width=0.03, head_length=0.01, fc='black', ec='black')
    
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.set_title('信号处理流程', fontsize=14, fontweight='bold')
    
    # 距离和多普勒计算示例
    ax5 = fig.add_subplot(2, 3, 5)
    
    # 计算示例数据
    t_example = np.linspace(0, T, 100)
    target_pos_t = p_target_initial + np.outer(t_example, v_target)
    
    # 计算到第一个接收站的距离
    rx1_pos = p_receivers[0]
    distances = np.linalg.norm(target_pos_t - rx1_pos, axis=1)
    
    # 计算径向速度
    vectors_to_target = target_pos_t - rx1_pos
    unit_vectors = vectors_to_target / distances[:, np.newaxis]
    radial_velocities = np.sum(v_target * unit_vectors, axis=1)
    
    # 计算多普勒频移
    doppler_shifts = (radial_velocities / c) * F_C
    
    # 绘制距离变化
    ax5_twin = ax5.twinx()
    
    line1 = ax5.plot(t_example * 1000, distances, 'b-', linewidth=2, label='距离变化')
    ax5.set_xlabel('时间 (ms)', fontsize=12)
    ax5.set_ylabel('距离 (m)', fontsize=12, color='blue')
    ax5.tick_params(axis='y', labelcolor='blue')
    
    line2 = ax5_twin.plot(t_example * 1000, doppler_shifts, 'r-', linewidth=2, label='多普勒频移')
    ax5_twin.set_ylabel('多普勒频移 (Hz)', fontsize=12, color='red')
    ax5_twin.tick_params(axis='y', labelcolor='red')
    
    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax5.legend(lines, labels, loc='upper right')
    
    ax5.set_title('距离与多普勒频移变化\n(目标到接收站1)', fontsize=14, fontweight='bold')
    ax5.grid(True, alpha=0.3)
    
    # IQ信号示例
    ax6 = fig.add_subplot(2, 3, 6)
    
    # 生成示例IQ信号（简化版）
    t_signal = np.linspace(0, 0.001, 1000)  # 1ms的信号
    distance_example = distances[0]  # 使用初始距离
    doppler_example = doppler_shifts[0]  # 使用初始多普勒
    
    # 信号参数
    amplitude = 1.0 / distance_example
    phase = 2 * np.pi * (F_C + doppler_example) * t_signal
    
    # 生成IQ信号
    I_signal = amplitude * np.cos(phase)
    Q_signal = amplitude * np.sin(phase)
    
    # 添加噪声
    noise_power = 0.1
    I_signal += np.random.normal(0, noise_power, len(I_signal))
    Q_signal += np.random.normal(0, noise_power, len(Q_signal))
    
    # 绘制IQ信号
    ax6.plot(t_signal * 1000, I_signal, 'b-', label='I分量', alpha=0.7)
    ax6.plot(t_signal * 1000, Q_signal, 'r-', label='Q分量', alpha=0.7)
    
    ax6.set_xlabel('时间 (ms)', fontsize=12)
    ax6.set_ylabel('幅度', fontsize=12)
    ax6.set_title('IQ信号示例\n(接收站1, 前1ms)', fontsize=14, fontweight='bold')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # 添加信号参数标注
    ax6.text(0.02, 0.98, f'初始距离: {distance_example:.1f} m\n'
                         f'多普勒频移: {doppler_example:.2f} Hz\n'
                         f'信号幅度: {amplitude:.2e}\n'
                         f'载波频率: {F_C/1e9:.1f} GHz', 
            transform=ax6.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('radar_scenario_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 打印详细参数说明
    print("=" * 80)
    print("雷达定位系统详细参数说明")
    print("=" * 80)
    
    print("\n1. 系统几何配置:")
    print(f"   - 接收站布局: 正方形阵列，边长2000米")
    print(f"   - 接收站坐标:")
    for i, pos in enumerate(p_receivers):
        print(f"     接收站{i+1}: ({pos[0]:6.0f}, {pos[1]:6.0f}, {pos[2]:3.0f}) 米")
    
    print(f"\n2. 信号参数:")
    print(f"   - 载波频率: {F_C/1e9:.1f} GHz (对应波长: {c/F_C:.2f} 米)")
    print(f"   - 采样频率: {F_S/1e6:.1f} MHz (采样间隔: {1/F_S*1e6:.1f} 微秒)")
    print(f"   - 观测时长: {T*1000:.1f} 毫秒")
    print(f"   - 序列长度: {int(T * F_S):,} 个采样点")
    
    print(f"\n3. 目标运动参数:")
    print(f"   - 初始位置: ({p_target_initial[0]:.0f}, {p_target_initial[1]:.0f}, {p_target_initial[2]:.0f}) 米")
    print(f"   - 运动速度: ({v_target[0]:.0f}, {v_target[1]:.0f}, {v_target[2]:.0f}) 米/秒")
    print(f"   - 最终位置: ({trajectory[-1, 0]:.0f}, {trajectory[-1, 1]:.0f}, {trajectory[-1, 2]:.0f}) 米")
    print(f"   - 总位移: {np.linalg.norm(trajectory[-1] - p_target_initial):.1f} 米")
    
    print(f"\n4. 信号传播特性:")
    print(f"   - 初始距离到各接收站:")
    for i, rx_pos in enumerate(p_receivers):
        dist = np.linalg.norm(p_target_initial - rx_pos)
        delay = dist / c
        print(f"     到接收站{i+1}: {dist:.1f} 米 (传播延迟: {delay*1e6:.2f} 微秒)")
    
    print(f"\n5. 多普勒效应:")
    for i, rx_pos in enumerate(p_receivers):
        vector_to_target = p_target_initial - rx_pos
        distance = np.linalg.norm(vector_to_target)
        unit_vector = vector_to_target / distance
        radial_velocity = np.dot(v_target, unit_vector)
        doppler_shift = (radial_velocity / c) * F_C
        print(f"   - 接收站{i+1}: 径向速度 {radial_velocity:.1f} m/s, 多普勒频移 {doppler_shift:.2f} Hz")
    
    print(f"\n6. 数据格式:")
    print(f"   - 输入特征: 每个时间点8维 (4个接收站 × 2个IQ分量)")
    print(f"   - 输出标签: 3维坐标 (x, y, z)")
    print(f"   - 训练样本: 每个样本包含 {int(T * F_S):,} 个时间点的IQ数据")

if __name__ == "__main__":
    print("开始生成IQ数据场景可视化...")
    visualize_radar_scenario()
    print("\n可视化完成！图像已保存为 'radar_scenario_visualization.png'")
